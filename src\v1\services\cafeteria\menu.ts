import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';

export const menuService = {
  getAllMenuItems: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search: string = (query.search as string) || '';
    const available = query.available !== undefined ? query.available === 'true' : undefined;

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              {
                menuCategory: {
                  name: { contains: search, mode: 'insensitive' },
                },
              },
            ],
          }
        : {}),
      ...(locationId !== undefined ? { locationId } : {}),
      ...(available !== undefined ? { isAvailable: available } : {}),
    };

    const [menu, totalCount] = await db.$transaction([
      db.cafeteriaMenu.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          menuCategory: {
            select: {
              name: true,
            },
          },
        },
      }),
      db.cafeteriaMenu.count({
        where: whereClause,
      }),
    ]);

    const response = {
      menu: menu,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },

  getMenuByCategory: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    return db.cafeteriaMenuCategory.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  },

  createMenuItem: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }
    const locationId = await auth.getLocationId();


    const { name,category, ...rest } = reqBody;
    const formatted = formatString.trimString(name);
    const existingMenu = await db.cafeteriaMenu.findFirst({
      where: {
        name: {
          equals: formatted,
          mode: 'insensitive',
        },
      },
    });

    if (existingMenu) {
      throw new HttpError('Menu item already exists', 400);
    }
    await db.cafeteriaMenu.create({
      data:{
        name: formatted,
        locationId: Number(locationId),
        menuCategoryId: Number(category),
        ...rest,
      }
    });
    return { message: 'Menu item created successfully' };
  },

  updateMenuItem: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);

    }    const { menuId, ...rest } = reqBody;

    const menuItem = await db.cafeteriaMenu.findUnique({
      where: { id: Number(menuId) },
    });
    if (!menuItem) {
      throw new HttpError('Menu item not found', 404);
    }
    await db.cafeteriaMenu.update({
      where: { id: Number(menuId) },
      data:{
        ...rest,
      }
    });
    return {message: "Menu item updated succesfully"}
  },
};
